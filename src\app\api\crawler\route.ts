import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import * as cheerio from 'cheerio';
import TurndownService from 'turndown';
import fs from 'fs';
import path from 'path';
import { URL } from 'url';
import iconv from 'iconv-lite';

// 编码检测和转换函数
function detectAndDecodeHtml(buffer: Buffer, contentType: string): string {
  let encoding = 'utf-8';

  // 尝试从Content-Type头部获取编码
  const charsetMatch = contentType.match(/charset=([^;]+)/i);
  if (charsetMatch) {
    encoding = charsetMatch[1].toLowerCase();
  } else {
    // 尝试从HTML meta标签中检测编码
    const htmlPreview = buffer.toString('utf-8', 0, Math.min(buffer.length, 1024));
    const metaCharsetMatch = htmlPreview.match(/<meta[^>]+charset=["']?([^"'\s>]+)/i);
    if (metaCharsetMatch) {
      encoding = metaCharsetMatch[1].toLowerCase();
    }
  }

  // 处理常见的中文编码
  if (encoding.includes('gb') || encoding.includes('gbk') || encoding.includes('gb2312')) {
    encoding = 'gbk';
  } else if (encoding.includes('big5')) {
    encoding = 'big5';
  }

  // 使用检测到的编码解码内容
  try {
    if (iconv.encodingExists(encoding)) {
      const decoded = iconv.decode(buffer, encoding);
      console.log(`使用编码: ${encoding}`);
      return decoded;
    } else {
      // 如果编码不支持，尝试常见编码
      const encodings = ['utf-8', 'gbk', 'gb2312', 'big5'];
      for (const enc of encodings) {
        try {
          const decoded = iconv.decode(buffer, enc);
          console.log(`自动检测编码: ${enc}`);
          return decoded;
        } catch (e) {
          continue;
        }
      }
      // 最后回退到UTF-8
      return buffer.toString('utf-8');
    }
  } catch (error) {
    console.warn(`编码转换失败，使用UTF-8: ${error}`);
    return buffer.toString('utf-8');
  }
}

// 爬虫配置接口
interface CrawlerConfig {
  url: string;
  title?: string;
  outputDir?: string;
  selectors?: {
    title?: string;
    content?: string;
    removeElements?: string[];
  };
  batch?: {
    enabled: boolean;
    nextPageSelector?: string;
    maxPages?: number;
    delay?: number;
  };
}

// 爬虫结果接口
interface CrawlerResult {
  success: boolean;
  message: string;
  filePath?: string;
  title?: string;
  imageCount?: number;
  pagesProcessed?: number;
  files?: string[];
}

export async function POST(request: NextRequest) {
  try {
    const config: CrawlerConfig = await request.json();
    
    if (!config.url) {
      return NextResponse.json(
        { success: false, message: 'URL is required' },
        { status: 400 }
      );
    }

    const result = config.batch?.enabled
      ? await crawlBatchPages(config)
      : await crawlPage(config);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Crawler error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Unknown error occurred' 
      },
      { status: 500 }
    );
  }
}

// 主要爬虫函数
async function crawlPage(config: CrawlerConfig): Promise<CrawlerResult> {
  const { url, outputDir = 'blog/爬虫内容', selectors = {} } = config;
  
  try {
    // 1. 获取页面内容
    console.log(`开始爬取: ${url}`);
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate'
      },
      timeout: 30000,
      responseType: 'arraybuffer' // 获取原始字节数据
    });

    // 2. 检测和处理字符编码
    const buffer = Buffer.from(response.data);
    const contentType = response.headers['content-type'] || '';
    const html = detectAndDecodeHtml(buffer, contentType);

    const $ = cheerio.load(html);
    
    // 2. 提取标题
    let title = config.title;
    if (!title) {
      if (selectors.title) {
        title = $(selectors.title).first().text().trim();
      } else {
        // 默认标题选择器
        title = $('h1').first().text().trim() || 
                $('title').text().trim() || 
                $('.title').first().text().trim() ||
                'untitled';
      }
    }
    
    // 清理标题，移除特殊字符
    title = sanitizeFileName(title);
    
    // 3. 提取内容
    let contentElement;
    if (selectors.content) {
      contentElement = $(selectors.content);
    } else {
      // 默认内容选择器
      contentElement = $('.content, .article-content, .post-content, main, article').first();
      if (contentElement.length === 0) {
        contentElement = $('body');
      }
    }
    
    // 4. 移除不需要的元素
    const defaultRemoveSelectors = [
      'script', 'style', 'nav', 'header', 'footer', 
      '.advertisement', '.ads', '.sidebar', '.menu',
      '.comment', '.share', '.social', '.related'
    ];
    
    const removeSelectors = [...defaultRemoveSelectors, ...(selectors.removeElements || [])];
    removeSelectors.forEach(selector => {
      contentElement.find(selector).remove();
    });
    
    // 5. 处理图片
    const imageCount = await processImages($, contentElement, url, outputDir, title);
    
    // 6. 转换为Markdown
    const turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced'
    });
    
    // 自定义转换规则
    turndownService.addRule('removeEmptyElements', {
      filter: function (node) {
        return node.nodeName === 'DIV' && !node.textContent?.trim() && !node.querySelector('img');
      },
      replacement: function () {
        return '';
      }
    });
    
    const markdownContent = turndownService.turndown(contentElement.html() || '');
    
    // 7. 生成Front Matter
    const frontMatter = generateFrontMatter(title, url);
    const fullContent = `${frontMatter}\n\n${markdownContent}`;
    
    // 8. 保存文件
    const filePath = await saveMarkdownFile(fullContent, title, outputDir);
    
    return {
      success: true,
      message: '爬取成功',
      filePath,
      title,
      imageCount
    };
    
  } catch (error) {
    console.error('爬取失败:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : '爬取失败'
    };
  }
}

// 处理图片下载
async function processImages(
  $: cheerio.CheerioAPI, 
  contentElement: cheerio.Cheerio<cheerio.Element>, 
  baseUrl: string, 
  outputDir: string, 
  title: string
): Promise<number> {
  const images = contentElement.find('img');
  let imageCount = 0;
  
  // 创建图片存储目录
  const imageDir = path.join(process.cwd(), 'public', outputDir, 'images', sanitizeFileName(title));
  if (!fs.existsSync(imageDir)) {
    fs.mkdirSync(imageDir, { recursive: true });
  }
  
  for (let i = 0; i < images.length; i++) {
    const img = images.eq(i);
    const src = img.attr('src');
    
    if (!src) continue;
    
    try {
      // 处理相对URL
      const imageUrl = new URL(src, baseUrl).href;
      
      // 获取文件扩展名
      const urlPath = new URL(imageUrl).pathname;
      const ext = path.extname(urlPath) || '.jpg';
      const fileName = `image_${i + 1}${ext}`;
      const localPath = path.join(imageDir, fileName);
      
      // 下载图片
      const imageResponse = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 15000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      fs.writeFileSync(localPath, imageResponse.data);
      
      // 更新图片src为本地路径
      const relativePath = `/${outputDir}/images/${sanitizeFileName(title)}/${fileName}`;
      img.attr('src', relativePath);
      
      imageCount++;
      console.log(`下载图片: ${fileName}`);
      
    } catch (error) {
      console.error(`下载图片失败: ${src}`, error);
      // 保留原始链接
    }
  }
  
  return imageCount;
}

// 生成Front Matter
function generateFrontMatter(title: string, url: string): string {
  const now = new Date();
  const date = now.toISOString().split('T')[0];
  
  return `---
title: ${title}
date: ${date}
source: ${url}
crawled_at: ${now.toISOString()}
author: 爬虫抓取
category: 爬虫内容
tags: [爬虫, 自动抓取]
---`;
}

// 保存Markdown文件
async function saveMarkdownFile(content: string, title: string, outputDir: string): Promise<string> {
  const fileName = `${sanitizeFileName(title)}.md`;
  const dirPath = path.join(process.cwd(), 'public', outputDir);
  
  // 确保目录存在
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
  
  const filePath = path.join(dirPath, fileName);
  fs.writeFileSync(filePath, content, 'utf-8');
  
  return path.join(outputDir, fileName);
}

// 批量爬取页面
async function crawlBatchPages(config: CrawlerConfig): Promise<CrawlerResult> {
  const { batch, outputDir = 'blog/爬虫内容' } = config;
  const maxPages = batch?.maxPages || 10;
  const delay = batch?.delay || 2000;
  const nextPageSelector = batch?.nextPageSelector || 'a[href*="next"], .next, .next-page';

  const results: string[] = [];
  let currentUrl = config.url;
  let pageCount = 0;
  let totalImageCount = 0;

  try {
    while (pageCount < maxPages && currentUrl) {
      console.log(`爬取第 ${pageCount + 1} 页: ${currentUrl}`);

      // 爬取当前页面
      const pageConfig = { ...config, url: currentUrl };
      const result = await crawlPage(pageConfig);

      if (!result.success) {
        console.error(`第 ${pageCount + 1} 页爬取失败:`, result.message);
        break;
      }

      if (result.filePath) {
        results.push(result.filePath);
      }

      if (result.imageCount) {
        totalImageCount += result.imageCount;
      }

      pageCount++;

      // 如果只有一页，直接返回
      if (pageCount === 1 && !batch?.enabled) {
        break;
      }

      // 获取下一页链接
      const response = await axios.get(currentUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate'
        },
        timeout: 30000,
        responseType: 'arraybuffer'
      });

      // 处理编码
      const buffer = Buffer.from(response.data);
      const contentType = response.headers['content-type'] || '';
      const html = detectAndDecodeHtml(buffer, contentType);

      const $ = cheerio.load(html);
      const nextLink = $(nextPageSelector).first().attr('href');

      if (!nextLink) {
        console.log('未找到下一页链接，批量爬取结束');
        break;
      }

      // 处理相对URL
      currentUrl = new URL(nextLink, currentUrl).href;

      // 延迟避免过于频繁的请求
      if (pageCount < maxPages) {
        console.log(`等待 ${delay}ms 后继续...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return {
      success: true,
      message: `批量爬取完成，共处理 ${pageCount} 页`,
      pagesProcessed: pageCount,
      files: results,
      imageCount: totalImageCount
    };

  } catch (error) {
    console.error('批量爬取失败:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : '批量爬取失败',
      pagesProcessed: pageCount,
      files: results,
      imageCount: totalImageCount
    };
  }
}

// 清理文件名
function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[<>:"/\\|?*]/g, '') // 移除Windows不允许的字符
    .replace(/\s+/g, '_') // 空格替换为下划线
    .replace(/_{2,}/g, '_') // 多个下划线合并为一个
    .trim()
    .substring(0, 100); // 限制长度
}
