'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import {
  ArrowLeft,
  BookOpen,
  Clock,
  User,
  Calendar,
  Tag,
  FileText,
  Loader2,
  AlertCircle,
  Eye,
  Share2,
  Bookmark,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import ReadingProgress from '@/components/ReadingProgress';
import TableOfContents from '@/components/TableOfContents';
import ReadingSettings from '@/components/ReadingSettings';
import TextToSpeech from '@/components/TextToSpeech';

interface FileContent {
  type: 'markdown' | 'word' | 'text';
  content: string;
  fileName: string;
  frontMatter?: {
    title?: string;
    date?: string;
    author?: string;
    tags?: string[];
    category?: string;
    [key: string]: any;
  };
}

interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  extension?: string;
  children?: FileItem[];
}

export default function ReadPage() {
  const params = useParams();
  const [content, setContent] = useState<FileContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [allFiles, setAllFiles] = useState<FileItem[]>([]);
  const [currentFileIndex, setCurrentFileIndex] = useState(-1);
  const [readingTime, setReadingTime] = useState(0);
  const [isBookmarked, setIsBookmarked] = useState(false);

  // 构建文件路径 - 处理可能的编码问题
  const rawPath = Array.isArray(params.path)
    ? params.path.join('/')
    : params.path;

  // 确保路径被正确解码
  let decodedPath = rawPath || '';
  try {
    // 如果路径包含编码字符，尝试解码
    if (rawPath && rawPath.includes('%')) {
      decodedPath = decodeURIComponent(rawPath);
    }
  } catch (error) {
    console.warn('Failed to decode path:', rawPath, error);
    decodedPath = rawPath || '';
  }

  // 如果路径已经包含blog前缀，就不要重复添加
  const filePath = decodedPath.startsWith('blog/') ? decodedPath : `blog/${decodedPath}`;

  useEffect(() => {
    fetchContent();
    fetchAllFiles();
  }, [filePath]);

  useEffect(() => {
    if (content) {
      calculateReadingTime();
      checkBookmarkStatus();
    }
  }, [content]);

  const fetchContent = async () => {
    setLoading(true);
    setError(null);

    try {
      // 使用URLSearchParams来正确构建查询字符串
      const searchParams = new URLSearchParams();
      searchParams.set('path', filePath);
      const response = await fetch(`/api/file-content?${searchParams.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch content');
      }

      const data = await response.json();
      setContent(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchAllFiles = async () => {
    try {
      const response = await fetch('/api/files?path=blog');
      if (response.ok) {
        const files = await response.json();
        setAllFiles(files);
        findCurrentFileIndex(files);
      }
    } catch (err) {
      console.error('Failed to fetch file list:', err);
    }
  };

  const findCurrentFileIndex = (files: FileItem[]) => {
    const flatFiles = flattenFiles(files);
    const index = flatFiles.findIndex(file => file.path === filePath);
    setCurrentFileIndex(index);
  };

  const flattenFiles = (files: FileItem[]): FileItem[] => {
    const result: FileItem[] = [];
    
    const traverse = (items: FileItem[]) => {
      items.forEach(item => {
        if (item.type === 'file') {
          result.push(item);
        } else if (item.children) {
          traverse(item.children);
        }
      });
    };
    
    traverse(files);
    return result;
  };

  const calculateReadingTime = () => {
    if (!content?.content) return;
    
    const wordsPerMinute = 200; // 平均阅读速度
    const wordCount = content.content.split(/\s+/).length;
    const time = Math.ceil(wordCount / wordsPerMinute);
    setReadingTime(time);
  };

  const checkBookmarkStatus = () => {
    if (typeof window === 'undefined') return;

    const bookmarks = JSON.parse(localStorage.getItem('bookmarks') || '[]');
    setIsBookmarked(bookmarks.includes(filePath));
  };

  const toggleBookmark = () => {
    if (typeof window === 'undefined') return;

    const bookmarks = JSON.parse(localStorage.getItem('bookmarks') || '[]');

    if (isBookmarked) {
      const newBookmarks = bookmarks.filter((path: string) => path !== filePath);
      localStorage.setItem('bookmarks', JSON.stringify(newBookmarks));
      setIsBookmarked(false);
    } else {
      bookmarks.push(filePath);
      localStorage.setItem('bookmarks', JSON.stringify(bookmarks));
      setIsBookmarked(true);
    }
  };

  const shareArticle = async () => {
    if (navigator.share && content?.frontMatter?.title) {
      try {
        await navigator.share({
          title: content.frontMatter.title,
          text: content.frontMatter.title,
          url: window.location.href,
        });
      } catch (err) {
        console.log('分享失败:', err);
      }
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href);
      alert('链接已复制到剪贴板');
    }
  };

  const navigateToFile = (direction: 'prev' | 'next') => {
    const flatFiles = flattenFiles(allFiles);
    const newIndex = direction === 'prev' ? currentFileIndex - 1 : currentFileIndex + 1;

    if (newIndex >= 0 && newIndex < flatFiles.length) {
      const targetFile = flatFiles[newIndex];
      const newPath = targetFile.path.replace('blog/', '');
      window.location.href = `/read/${newPath}`;
    }
  };

  const handleAutoNext = () => {
    console.log('handleAutoNext 被调用');
    console.log('当前文件路径:', filePath);
    console.log('当前文件索引:', currentFileIndex);
    console.log('所有文件数量:', allFiles.length);

    // 获取当前文件的文件夹路径
    const currentDir = filePath.substring(0, filePath.lastIndexOf('/'));
    console.log('当前文件夹:', currentDir);

    const flatFiles = flattenFiles(allFiles);
    console.log('扁平化文件列表长度:', flatFiles.length);

    // 找到同一文件夹内的下一篇文章（支持.md和.docx文件）
    let nextFileIndex = -1;
    for (let i = currentFileIndex + 1; i < flatFiles.length; i++) {
      const file = flatFiles[i];
      const fileDir = file.path.substring(0, file.path.lastIndexOf('/'));
      console.log(`检查文件 ${i}: ${file.path}, 文件夹: ${fileDir}`);

      // 考虑同一文件夹内的.md和.docx文件
      if (fileDir === currentDir && (file.path.endsWith('.md') || file.path.endsWith('.docx'))) {
        nextFileIndex = i;
        console.log('找到下一篇文章，索引:', nextFileIndex);
        break;
      }
    }

    if (nextFileIndex !== -1) {
      const targetFile = flatFiles[nextFileIndex];
      const newPath = targetFile.path.replace('blog/', '');
      console.log('准备跳转到:', newPath);
      window.location.href = `/read/${newPath}`;
    } else {
      // 如果同一文件夹内没有下一篇，可以选择跳转到下一个文件夹的第一篇
      // 或者显示提示信息
      console.log('当前文件夹内没有更多文章了');
    }
  };

  // 追加下一篇内容到当前页面显示
  const appendNextContent = (nextContent: { content: string; title: string; path: string }) => {
    if (!content) return;

    console.log('追加下一篇内容到页面显示:', nextContent.title);

    // 创建分隔符
    const separator = content.type === 'word'
      ? '<hr style="margin: 2rem 0; border: 1px solid #e5e7eb;"><h2 style="color: #374151; margin-bottom: 1rem;">下一篇：' + nextContent.title + '</h2>'
      : '\n\n---\n\n## 下一篇：' + nextContent.title + '\n\n';

    // 追加内容
    const updatedContent = {
      ...content,
      content: content.content + separator + nextContent.content
    };

    setContent(updatedContent);
  };

  const getNextContent = async (): Promise<{ content: string; title: string; path: string } | null> => {
    try {
      console.log('🔍 getNextContent 被调用');

      // 获取当前文件的文件夹路径
      const currentDir = filePath.substring(0, filePath.lastIndexOf('/'));
      const flatFiles = flattenFiles(allFiles);

      console.log('查找下一篇文章的详细信息:', {
        currentFilePath: filePath,
        currentDir: currentDir,
        currentFileIndex: currentFileIndex,
        totalFiles: flatFiles.length
      });

      // 显示当前文件夹内的所有文件
      const currentDirFiles = flatFiles.filter((file, index) => {
        const fileDir = file.path.substring(0, file.path.lastIndexOf('/'));
        return fileDir === currentDir;
      });

      console.log('当前文件夹内的所有文件:', currentDirFiles.map((file, index) => ({
        index: flatFiles.indexOf(file),
        name: file.name,
        path: file.path,
        isSupported: file.path.endsWith('.md') || file.path.endsWith('.docx')
      })));

      // 找到同一文件夹内的下一篇文章（只考虑.md文件）
      let nextFileIndex = -1;
      for (let i = currentFileIndex + 1; i < flatFiles.length; i++) {
        const file = flatFiles[i];
        const fileDir = file.path.substring(0, file.path.lastIndexOf('/'));

        console.log(`检查文件 ${i}:`, {
          name: file.name,
          path: file.path,
          fileDir: fileDir,
          isInSameDir: fileDir === currentDir,
          isSupported: file.path.endsWith('.md') || file.path.endsWith('.docx')
        });

        // 考虑同一文件夹内的.md和.docx文件
        if (fileDir === currentDir && (file.path.endsWith('.md') || file.path.endsWith('.docx'))) {
          nextFileIndex = i;
          console.log('✅ 找到下一篇文件:', file.path);
          break;
        }
      }

      if (nextFileIndex !== -1) {
        const targetFile = flatFiles[nextFileIndex];
        const nextPath = targetFile.path; // 保持完整路径，包括blog/前缀

        console.log('找到下一篇文章:', {
          targetFile: targetFile.name,
          originalPath: targetFile.path,
          nextPath: nextPath
        });

        // 获取下一篇文章的内容
        const apiUrl = `/api/file-content?path=${encodeURIComponent(nextPath)}`;
        console.log('API请求URL:', apiUrl);

        const response = await fetch(apiUrl);
        console.log('API响应状态:', response.status);

        if (response.ok) {
          const nextContent = await response.json();
          console.log('成功获取下一篇内容:', {
            title: nextContent.frontMatter?.title || targetFile.name,
            contentLength: nextContent.content?.length || 0
          });

          return {
            content: nextContent.content,
            title: nextContent.frontMatter?.title || targetFile.name,
            path: nextPath
          };
        } else {
          console.error('API请求失败:', response.status, response.statusText);
          const errorText = await response.text();
          console.error('错误详情:', errorText);
        }
      } else {
        console.log('没有找到下一篇文章');
      }

      return null;
    } catch (error) {
      console.error('获取下一篇内容失败:', error);
      return null;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 mx-auto mb-4 text-blue-500 animate-spin" />
          <p className="text-gray-600">正在加载文章...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center text-red-500 max-w-md mx-auto p-6">
          <AlertCircle className="w-16 h-16 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">加载失败</h2>
          <p className="text-sm mb-4">{error}</p>
          <Link 
            href="/"
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            返回首页
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 阅读进度条 */}
      <ReadingProgress />

      {/* 目录导航 */}
      {content?.type === 'markdown' && (
        <TableOfContents content={content.content} />
      )}

      {/* 阅读设置 */}
      <ReadingSettings />

      {/* 语音朗读 */}
      {content?.content && (
        <TextToSpeech
          content={content.content}
          currentFilePath={filePath}
          onAutoNext={handleAutoNext}
          onGetNextContent={getNextContent}
          onAppendContent={appendNextContent}
        />
      )}



      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href="/" 
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="hidden sm:inline">返回首页</span>
              </Link>
              
              <div className="flex items-center gap-2 text-gray-400">
                <BookOpen className="w-5 h-5" />
                <span className="text-sm">阅读模式</span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* 阅读时间 */}
              {readingTime > 0 && (
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Clock className="w-4 h-4" />
                  <span>{readingTime} 分钟</span>
                </div>
              )}

              {/* 操作按钮 */}
              <button
                onClick={toggleBookmark}
                className={`p-2 rounded-lg transition-colors ${
                  isBookmarked 
                    ? 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                title={isBookmarked ? '取消收藏' : '收藏文章'}
              >
                <Bookmark className="w-4 h-4" />
              </button>

              <button
                onClick={shareArticle}
                className="p-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors"
                title="分享文章"
              >
                <Share2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <article className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* 文章头部 */}
          <header className="p-8 border-b border-gray-200">
            {content?.frontMatter?.title && (
              <h1 className="text-3xl font-bold text-gray-900 mb-6 leading-tight">
                {content.frontMatter.title}
              </h1>
            )}

            {/* 文章元信息 */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-4">
              {content?.frontMatter?.author && (
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <span>{content.frontMatter.author}</span>
                </div>
              )}

              {content?.frontMatter?.date && (
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(content.frontMatter.date)}</span>
                </div>
              )}

              {content?.frontMatter?.category && (
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  <span>{content.frontMatter.category}</span>
                </div>
              )}

              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                <span>阅读时间 {readingTime} 分钟</span>
              </div>
            </div>

            {/* 标签 */}
            {content?.frontMatter?.tags && content.frontMatter.tags.length > 0 && (
              <div className="flex items-center gap-2">
                <Tag className="w-4 h-4 text-gray-500" />
                <div className="flex flex-wrap gap-2">
                  {content.frontMatter.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </header>

          {/* 文章内容 */}
          <div className="p-8">
            <div className="prose prose-lg max-w-none">
              {content?.type === 'markdown' && (
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    code: ({ node, inline, className, children, ...props }) => {
                      const match = /language-(\w+)/.exec(className || '');
                      return !inline && match ? (
                        <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto my-4">
                          <code className={className} {...props}>
                            {children}
                          </code>
                        </pre>
                      ) : (
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono" {...props}>
                          {children}
                        </code>
                      );
                    },
                    blockquote: ({ children }) => (
                      <blockquote className="border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-50 italic">
                        {children}
                      </blockquote>
                    ),
                    table: ({ children }) => (
                      <div className="overflow-x-auto my-4">
                        <table className="min-w-full border border-gray-300">
                          {children}
                        </table>
                      </div>
                    ),
                    th: ({ children }) => (
                      <th className="border border-gray-300 px-4 py-2 bg-gray-50 font-semibold text-left">
                        {children}
                      </th>
                    ),
                    td: ({ children }) => (
                      <td className="border border-gray-300 px-4 py-2">
                        {children}
                      </td>
                    ),
                  }}
                >
                  {content.content}
                </ReactMarkdown>
              )}

              {content?.type === 'word' && (
                <div dangerouslySetInnerHTML={{ __html: content.content }} />
              )}

              {content?.type === 'text' && (
                <pre className="whitespace-pre-wrap font-mono text-sm bg-gray-50 p-4 rounded-lg">
                  {content.content}
                </pre>
              )}
            </div>
          </div>
        </article>

        {/* 导航按钮 */}
        <div className="flex justify-between items-center mt-8 mb-20">
          <button
            onClick={() => navigateToFile('prev')}
            disabled={currentFileIndex <= 0}
            className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>上一篇</span>
          </button>

          <Link
            href="/"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
          >
            返回列表
          </Link>

          <button
            onClick={() => navigateToFile('next')}
            disabled={currentFileIndex >= flattenFiles(allFiles).length - 1}
            className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm"
          >
            <span>下一篇</span>
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </main>
    </div>
  );
}
