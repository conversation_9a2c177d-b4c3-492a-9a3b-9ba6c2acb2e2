'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Settings, Type, Palette, Sun, Moon, Monitor, Move } from 'lucide-react';

interface ReadingSettingsProps {
  className?: string;
}

export default function ReadingSettings({ className = '' }: ReadingSettingsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [theme, setTheme] = useState<'light' | 'dark' | 'sepia'>('light');
  const [fontFamily, setFontFamily] = useState<'default' | 'serif' | 'mono'>('default');
  const [position, setPosition] = useState({ x: 24, y: 24 }); // 默认位置 (left: 24px, bottom: 24px)
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    // 只在客户端加载设置
    if (typeof window === 'undefined') return;

    // 从localStorage加载设置
    const savedFontSize = localStorage.getItem('reading-font-size');
    const savedTheme = localStorage.getItem('reading-theme');
    const savedFontFamily = localStorage.getItem('reading-font-family');
    const savedPosition = localStorage.getItem('reading-settings-position');

    if (savedFontSize) setFontSize(parseInt(savedFontSize));
    if (savedTheme) setTheme(savedTheme as any);
    if (savedFontFamily) setFontFamily(savedFontFamily as any);
    if (savedPosition) {
      try {
        setPosition(JSON.parse(savedPosition));
      } catch (e) {
        // 如果解析失败，使用默认位置
      }
    }
  }, []);

  useEffect(() => {
    // 应用设置到文档
    applySettings();
  }, [fontSize, theme, fontFamily]);

  const applySettings = () => {
    if (typeof window === 'undefined') return;

    const article = document.querySelector('article');
    if (!article) return;

    // 应用字体大小
    article.style.fontSize = `${fontSize}px`;

    // 应用字体族
    const fontFamilyMap = {
      default: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      serif: 'Georgia, "Times New Roman", serif',
      mono: '"SF Mono", Monaco, "Cascadia Code", monospace',
    };
    article.style.fontFamily = fontFamilyMap[fontFamily];

    // 应用主题
    const body = document.body;
    body.classList.remove('theme-light', 'theme-dark', 'theme-sepia');
    body.classList.add(`theme-${theme}`);

    // 保存到localStorage
    localStorage.setItem('reading-font-size', fontSize.toString());
    localStorage.setItem('reading-theme', theme);
    localStorage.setItem('reading-font-family', fontFamily);
  };

  const adjustFontSize = (delta: number) => {
    const newSize = Math.max(12, Math.min(24, fontSize + delta));
    setFontSize(newSize);
  };

  // 拖拽功能
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!buttonRef.current) return;

    setIsDragging(true);
    const rect = buttonRef.current.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;

      const newX = Math.max(0, Math.min(window.innerWidth - 60, e.clientX - dragOffset.x));
      const newY = Math.max(0, Math.min(window.innerHeight - 60, e.clientY - dragOffset.y));

      setPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false);
        // 保存位置到localStorage
        localStorage.setItem('reading-settings-position', JSON.stringify(position));
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset, position]);

  return (
    <>
      {/* 设置按钮 */}
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        onMouseDown={handleMouseDown}
        className={`
          fixed z-40 bg-gray-800 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition-colors
          ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}
        `}
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
        }}
        title="阅读设置 (可拖拽)"
      >
        <Settings className='w-5 h-5' />
      </button>

      {/* 设置面板 */}
      <div
        className={`
        fixed w-72 bg-white border border-gray-200 rounded-lg shadow-lg z-30
        transition-all duration-300 overflow-hidden
        ${isOpen ? 'opacity-100 visible transform translate-y-0' : 'opacity-0 invisible transform translate-y-4'}
        ${className}
      `}
        style={{
          left: `${typeof window !== 'undefined' ? Math.min(position.x, window.innerWidth - 288) : position.x}px`, // 288px = w-72
          top: `${Math.max(0, position.y - 320)}px`, // 面板显示在按钮上方
        }}
      >
        <div className='p-4 border-b border-gray-200'>
          <div className='flex items-center justify-between'>
            <h3 className='font-semibold text-gray-800'>阅读设置</h3>
            <button onClick={() => setIsOpen(false)} className='text-gray-500 hover:text-gray-700'>
              ×
            </button>
          </div>
          {/* 拖拽提示 */}
          <div className="flex items-center justify-center mt-2 pt-2 border-t border-gray-100">
            <Move className="w-4 h-4 text-gray-400 mr-2" />
            <span className="text-xs text-gray-500">按钮可拖拽移动</span>
          </div>
        </div>

        <div className='p-4 space-y-6'>
          {/* 字体大小 */}
          <div>
            <div className='flex items-center gap-2 mb-3'>
              <Type className='w-4 h-4 text-gray-600' />
              <span className='text-sm font-medium text-gray-700'>字体大小</span>
            </div>
            <div className='flex items-center gap-3'>
              <button
                onClick={() => adjustFontSize(-2)}
                className='px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors'
              >
                A-
              </button>
              <span className='text-sm text-gray-600 min-w-[3rem] text-center'>{fontSize}px</span>
              <button
                onClick={() => adjustFontSize(2)}
                className='px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors'
              >
                A+
              </button>
            </div>
          </div>

          {/* 字体族 */}
          <div>
            <div className='flex items-center gap-2 mb-3'>
              <Type className='w-4 h-4 text-gray-600' />
              <span className='text-sm font-medium text-gray-700'>字体</span>
            </div>
            <div className='grid grid-cols-3 gap-2'>
              {[
                { key: 'default', label: '默认' },
                { key: 'serif', label: '衬线' },
                { key: 'mono', label: '等宽' },
              ].map(font => (
                <button
                  key={font.key}
                  onClick={() => setFontFamily(font.key as any)}
                  className={`px-3 py-2 text-sm rounded transition-colors ${
                    fontFamily === font.key
                      ? 'bg-blue-100 text-blue-700 border border-blue-300'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {font.label}
                </button>
              ))}
            </div>
          </div>

          {/* 主题 */}
          <div>
            <div className='flex items-center gap-2 mb-3'>
              <Palette className='w-4 h-4 text-gray-600' />
              <span className='text-sm font-medium text-gray-700'>主题</span>
            </div>
            <div className='grid grid-cols-3 gap-2'>
              {[
                { key: 'light', label: '浅色', icon: Sun },
                { key: 'dark', label: '深色', icon: Moon },
                { key: 'sepia', label: '护眼', icon: Monitor },
              ].map(themeOption => {
                const Icon = themeOption.icon;
                return (
                  <button
                    key={themeOption.key}
                    onClick={() => setTheme(themeOption.key as any)}
                    className={`flex flex-col items-center gap-1 px-3 py-2 text-sm rounded transition-colors ${
                      theme === themeOption.key
                        ? 'bg-blue-100 text-blue-700 border border-blue-300'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Icon className='w-4 h-4' />
                    {themeOption.label}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 遮罩 */}
      {isOpen && <div className='fixed inset-0 bg-opacity-25 z-20' onClick={() => setIsOpen(false)} />}
    </>
  );
}
