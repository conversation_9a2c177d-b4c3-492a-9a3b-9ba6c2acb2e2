# 阅读界面拖拽功能说明

## 问题描述
在阅读页面中，阅读设置和朗读控制面板原本固定在页面底部，会遮挡住"上一篇"和"下一篇"的导航按钮，影响用户体验。

## 解决方案
为阅读设置和朗读控制面板添加了拖拽功能，用户可以自由移动这些控制面板到合适的位置。

## 功能特性

### 1. 阅读设置面板
- **位置**: 默认位置在页面左上角
- **拖拽**: 点击设置按钮并拖拽可移动位置
- **记忆**: 位置会自动保存到本地存储
- **视觉提示**: 鼠标悬停时显示抓手光标
- **面板跟随**: 设置面板会跟随按钮位置显示

### 2. 朗读控制面板
- **位置**: 默认位置在页面右上角
- **拖拽**: 点击控制面板并拖拽可移动位置
- **拖拽手柄**: 面板顶部有拖拽图标提示
- **记忆**: 位置会自动保存到本地存储
- **边界限制**: 拖拽时会限制在窗口范围内

### 3. 导航按钮优化
- **底部边距**: 增加了底部边距，避免被固定元素遮挡
- **视觉增强**: 添加了阴影效果，提升视觉层次

## 使用方法

### 移动阅读设置
1. 点击左下角的设置按钮（齿轮图标）
2. 按住鼠标左键拖拽到合适位置
3. 松开鼠标，位置会自动保存

### 移动朗读控制
1. 点击朗读控制面板的任意位置
2. 按住鼠标左键拖拽到合适位置
3. 松开鼠标，位置会自动保存

## 技术实现

### 核心功能
- 使用React Hooks管理拖拽状态
- 监听鼠标事件实现拖拽逻辑
- 使用localStorage保存位置信息
- 边界检测防止元素超出窗口

### 代码结构
```typescript
// 状态管理
const [position, setPosition] = useState({ x: 24, y: 24 });
const [isDragging, setIsDragging] = useState(false);
const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

// 拖拽事件处理
const handleMouseDown = (e: React.MouseEvent) => {
  setIsDragging(true);
  // 计算拖拽偏移量
};

// 鼠标移动和释放事件
useEffect(() => {
  const handleMouseMove = (e: MouseEvent) => {
    // 更新位置
  };
  const handleMouseUp = () => {
    // 保存位置
  };
}, [isDragging]);
```

## 用户体验改进
1. **解决遮挡问题**: 用户可以自由移动控制面板，不再遮挡导航按钮
2. **个性化布局**: 用户可以根据使用习惯调整控制面板位置
3. **位置记忆**: 下次访问时会恢复上次的位置设置
4. **视觉反馈**: 提供清晰的拖拽提示和光标变化
5. **响应式设计**: 在不同屏幕尺寸下都能正常工作

## 注意事项
- 拖拽功能仅在桌面端有效
- 移动端用户可以通过触摸操作使用基本功能
- 位置信息存储在浏览器本地，清除浏览器数据会重置位置
